"use client";

import { cn } from "@ui/lib";
import { CheckCircleIcon, ExternalLinkIcon } from "lucide-react";
import { MagicalButton } from "./MagicalButton";

interface WebsiteCreatedSuccessProps {
	websiteUrl?: string;
	businessName?: string;
	onContinue?: () => void;
	className?: string;
}

export function WebsiteCreatedSuccess({
	websiteUrl = "https://your-business.pintosite.com",
	businessName = "Your Business",
	onContinue,
	className
}: WebsiteCreatedSuccessProps) {
	return (
		<div className={cn(
			"flex flex-col items-center justify-center space-y-6 py-12 text-center",
			className
		)}>
			{/* Success icon */}
			<div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center">
				<CheckCircleIcon className="w-8 h-8 text-success" />
			</div>

			{/* Success message */}
			<div className="space-y-2">
				<h3 className="text-2xl font-bold text-foreground">
					Website Created Successfully!
				</h3>
				<p className="text-muted-foreground max-w-md">
					Your website for {businessName} is now live and ready to share with your customers.
				</p>
			</div>

			{/* Website preview */}
			<div className="bg-muted/50 rounded-lg p-4 border">
				<div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
					<span>Your website:</span>
				</div>
				<a 
					href={websiteUrl}
					target="_blank"
					rel="noopener noreferrer"
					className="flex items-center gap-2 text-primary hover:underline font-medium"
				>
					{websiteUrl}
					<ExternalLinkIcon className="w-4 h-4" />
				</a>
			</div>

			{/* Continue button */}
			<MagicalButton
				onClick={onContinue}
				variant="primary"
				className="px-8"
			>
				Continue to Dashboard
			</MagicalButton>
		</div>
	);
}
