"use client";

import { cn } from "@ui/lib";

interface CleanBackgroundProps {
	children: React.ReactNode;
	className?: string;
}

export function MagicalBackground({
	children,
	className,
}: CleanBackgroundProps) {
	return (
		<div className={cn("relative", className)}>
			{/* Subtle background */}
			<div className="absolute inset-0 bg-gradient-to-b from-background to-muted/20" />

			{/* Content */}
			<div className="relative z-10">{children}</div>
		</div>
	);
}
