"use client";

import { cn } from "@ui/lib";
import { useEffect, useState } from "react";

interface MagicalBackgroundProps {
	children: React.ReactNode;
	className?: string;
	intensity?: "low" | "medium" | "high";
}

export function MagicalBackground({ 
	children, 
	className, 
	intensity = "medium" 
}: MagicalBackgroundProps) {
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
	}, []);

	const intensityClasses = {
		low: "opacity-30",
		medium: "opacity-50", 
		high: "opacity-70"
	};

	return (
		<div className={cn("relative overflow-hidden", className)}>
			{/* Animated gradient background */}
			<div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/5 to-highlight/5" />
			
			{/* Floating gradient orbs */}
			<div className={cn(
				"absolute inset-0 transition-opacity duration-1000",
				mounted ? intensityClasses[intensity] : "opacity-0"
			)}>
				{/* Large floating orb */}
				<div className="absolute -top-20 -right-20 w-96 h-96 bg-gradient-to-br from-primary/20 to-accent/20 rounded-full blur-3xl animate-pulse" 
					 style={{ animationDuration: "4s" }} />
				
				{/* Medium floating orb */}
				<div className="absolute -bottom-32 -left-32 w-80 h-80 bg-gradient-to-tr from-highlight/20 to-primary/20 rounded-full blur-3xl animate-pulse" 
					 style={{ animationDuration: "6s", animationDelay: "2s" }} />
				
				{/* Small accent orb */}
				<div className="absolute top-1/3 left-1/4 w-32 h-32 bg-gradient-to-r from-accent/30 to-highlight/30 rounded-full blur-2xl animate-pulse" 
					 style={{ animationDuration: "3s", animationDelay: "1s" }} />
			</div>

			{/* Subtle grid pattern */}
			<div className="absolute inset-0 bg-[linear-gradient(rgba(78,109,245,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(78,109,245,0.03)_1px,transparent_1px)] bg-[size:20px_20px]" />
			
			{/* Content */}
			<div className="relative z-10">
				{children}
			</div>
		</div>
	);
}
