"use client";

import { cn } from "@ui/lib";
import { useEffect, useState } from "react";

export function AILoadingAnimation({
	message = "Creating your website",
	className,
}: { message?: string; className?: string }) {
	const [dots, setDots] = useState("");

	useEffect(() => {
		const interval = setInterval(() => {
			setDots((prev) => (prev.length >= 3 ? "" : `${prev}.`));
		}, 500);

		return () => clearInterval(interval);
	}, []);

	return (
		<div
			className={cn(
				"flex flex-col items-center justify-center space-y-6 py-12",
				className,
			)}
		>
			{/* Simple spinner */}
			<div className="relative">
				<div className="w-8 h-8 border-2 border-muted border-t-primary rounded-full animate-spin" />
			</div>

			{/* Loading message */}
			<div className="text-center space-y-2">
				<p className="text-base font-medium text-foreground">
					{message}
					{dots}
				</p>
				<p className="text-sm text-muted-foreground">
					This will only take a moment
				</p>
			</div>
		</div>
	);
}
