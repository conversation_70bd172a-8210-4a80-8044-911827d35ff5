"use client";

import { cn } from "@ui/lib";
import { BrainIcon, SparklesIcon, WandIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface AILoadingAnimationProps {
	message?: string;
	className?: string;
	variant?: "brain" | "sparkles" | "wand";
}

export function AILoadingAnimation({
	message = "AI is working its magic...",
	className,
	variant = "brain",
}: AILoadingAnimationProps) {
	const [dots, setDots] = useState("");
	const [pulseIndex, setPulseIndex] = useState(0);

	useEffect(() => {
		const interval = setInterval(() => {
			setDots((prev) => (prev.length >= 3 ? "" : `${prev}.`));
		}, 500);

		return () => clearInterval(interval);
	}, []);

	useEffect(() => {
		const interval = setInterval(() => {
			setPulseIndex((prev) => (prev + 1) % 3);
		}, 600);

		return () => clearInterval(interval);
	}, []);

	const IconComponent = {
		brain: BrainIcon,
		sparkles: SparklesIcon,
		wand: WandIcon,
	}[variant];

	return (
		<div
			className={cn(
				"flex flex-col items-center justify-center space-y-4 py-8",
				className,
			)}
		>
			{/* Main icon with pulsing effect */}
			<div className="relative">
				<div className="absolute inset-0 bg-primary/20 rounded-full animate-ping" />
				<div className="relative bg-gradient-to-br from-primary to-accent p-4 rounded-full">
					<IconComponent className="w-8 h-8 text-white animate-pulse" />
				</div>
			</div>

			{/* Floating dots animation */}
			<div className="flex space-x-2">
				{[0, 1, 2].map((index) => (
					<div
						key={index}
						className={cn(
							"w-2 h-2 rounded-full transition-all duration-300",
							pulseIndex === index
								? "bg-primary scale-125"
								: "bg-primary/30 scale-100",
						)}
					/>
				))}
			</div>

			{/* Loading message */}
			<div className="text-center space-y-2">
				<p className="text-sm font-medium text-foreground">
					{message}
					{dots}
				</p>
				<div className="flex items-center justify-center space-x-1 text-xs text-muted-foreground">
					<SparklesIcon className="w-3 h-3" />
					<span>Powered by AI</span>
					<SparklesIcon className="w-3 h-3" />
				</div>
			</div>

			{/* Progress bar with animated segments */}
			<div className="w-64 h-2 bg-muted rounded-full overflow-hidden relative">
				<div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-accent/20 to-highlight/20 animate-pulse" />
				<div
					className="h-full bg-gradient-to-r from-primary via-accent to-highlight rounded-full transition-all duration-1000 ease-out"
					style={{
						width: "75%",
						animation: "loading-progress 3s ease-in-out infinite",
					}}
				/>
				{/* Shimmer effect */}
				<div
					className="absolute top-0 left-0 h-full w-8 bg-gradient-to-r from-transparent via-white/30 to-transparent"
					style={{
						animation: "shimmer 2s ease-in-out infinite",
					}}
				/>
			</div>

			{/* AI Processing Steps */}
			<div className="text-xs text-muted-foreground space-y-1 max-w-xs text-center">
				<div className="flex items-center justify-center space-x-2">
					<div className="w-1 h-1 bg-primary rounded-full animate-pulse" />
					<span>Analyzing business data</span>
				</div>
				<div className="flex items-center justify-center space-x-2">
					<div
						className="w-1 h-1 bg-accent rounded-full animate-pulse"
						style={{ animationDelay: "0.5s" }}
					/>
					<span>Extracting key information</span>
				</div>
				<div className="flex items-center justify-center space-x-2">
					<div
						className="w-1 h-1 bg-highlight rounded-full animate-pulse"
						style={{ animationDelay: "1s" }}
					/>
					<span>Preparing your website</span>
				</div>
			</div>

			<style jsx>{`
				@keyframes loading-progress {
					0% { width: 20%; }
					50% { width: 85%; }
					100% { width: 20%; }
				}
				@keyframes shimmer {
					0% { transform: translateX(-100%); }
					100% { transform: translateX(400%); }
				}
			`}</style>
		</div>
	);
}
