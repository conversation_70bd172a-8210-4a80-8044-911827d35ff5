"use client";

import { cn } from "@ui/lib";
import { Input } from "@ui/components/input";
import { SparklesIcon, CheckCircleIcon, AlertCircleIcon } from "lucide-react";
import { forwardRef, useState, useEffect } from "react";

interface MagicalInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
	isValid?: boolean;
	isInvalid?: boolean;
	isLoading?: boolean;
	magicalEffect?: boolean;
}

export const MagicalInput = forwardRef<HTMLInputElement, MagicalInputProps>(
	({ className, isValid, isInvalid, isLoading, magicalEffect = true, ...props }, ref) => {
		const [isFocused, setIsFocused] = useState(false);
		const [hasValue, setHasValue] = useState(false);

		useEffect(() => {
			setHasValue(!!props.value || !!props.defaultValue);
		}, [props.value, props.defaultValue]);

		return (
			<div className="relative group">
				{/* Magical glow effect */}
				{magicalEffect && isFocused && (
					<div className="absolute -inset-1 bg-gradient-to-r from-primary/20 via-accent/20 to-highlight/20 rounded-lg blur-sm animate-pulse" />
				)}
				
				{/* Input container */}
				<div className="relative">
					<Input
						ref={ref}
						className={cn(
							"transition-all duration-300 pr-10",
							isFocused && magicalEffect && "ring-2 ring-primary/50 border-primary/50",
							isValid && "border-success ring-1 ring-success/20",
							isInvalid && "border-destructive ring-1 ring-destructive/20",
							className
						)}
						onFocus={(e) => {
							setIsFocused(true);
							props.onFocus?.(e);
						}}
						onBlur={(e) => {
							setIsFocused(false);
							props.onBlur?.(e);
						}}
						onChange={(e) => {
							setHasValue(!!e.target.value);
							props.onChange?.(e);
						}}
						{...props}
					/>

					{/* Status icons */}
					<div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center">
						{isLoading && (
							<div className="animate-spin w-4 h-4 border-2 border-primary border-t-transparent rounded-full" />
						)}
						{!isLoading && isValid && (
							<CheckCircleIcon className="w-4 h-4 text-success animate-in fade-in duration-300" />
						)}
						{!isLoading && isInvalid && (
							<AlertCircleIcon className="w-4 h-4 text-destructive animate-in fade-in duration-300" />
						)}
						{!isLoading && !isValid && !isInvalid && isFocused && magicalEffect && (
							<SparklesIcon className="w-4 h-4 text-primary/60 animate-pulse" />
						)}
					</div>
				</div>

				{/* Floating sparkles effect */}
				{magicalEffect && isFocused && (
					<div className="absolute inset-0 pointer-events-none">
						{[...Array(3)].map((_, i) => (
							<SparklesIcon
								key={i}
								className={cn(
									"absolute w-2 h-2 text-primary/40 animate-ping",
									i === 0 && "top-2 right-2",
									i === 1 && "bottom-2 left-2 animation-delay-500",
									i === 2 && "top-1/2 left-1/2 animation-delay-1000"
								)}
								style={{
									animationDuration: "2s",
									animationDelay: `${i * 0.5}s`
								}}
							/>
						))}
					</div>
				)}
			</div>
		);
	}
);

MagicalInput.displayName = "MagicalInput";
