"use client";

import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { AlertCircleIcon, CheckCircleIcon, Loader2Icon } from "lucide-react";
import { forwardRef, useState } from "react";

interface CleanInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
	isValid?: boolean;
	isInvalid?: boolean;
	isLoading?: boolean;
}

export const MagicalInput = forwardRef<HTMLInputElement, CleanInputProps>(
	({ className, isValid, isInvalid, isLoading, ...props }, ref) => {
		const [isFocused, setIsFocused] = useState(false);

		return (
			<div className="relative">
				<Input
					ref={ref}
					className={cn(
						"transition-all duration-200 pr-10",
						isFocused && "ring-2 ring-primary/20 border-primary/50",
						isValid && "border-success/50 ring-1 ring-success/10",
						isInvalid &&
							"border-destructive/50 ring-1 ring-destructive/10",
						className,
					)}
					onFocus={(e) => {
						setIsFocused(true);
						props.onFocus?.(e);
					}}
					onBlur={(e) => {
						setIsFocused(false);
						props.onBlur?.(e);
					}}
					{...props}
				/>

				{/* Status icons */}
				<div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center">
					{isLoading && (
						<Loader2Icon className="w-4 h-4 text-muted-foreground animate-spin" />
					)}
					{!isLoading && isValid && (
						<CheckCircleIcon className="w-4 h-4 text-success" />
					)}
					{!isLoading && isInvalid && (
						<AlertCircleIcon className="w-4 h-4 text-destructive" />
					)}
				</div>
			</div>
		);
	},
);

MagicalInput.displayName = "MagicalInput";
