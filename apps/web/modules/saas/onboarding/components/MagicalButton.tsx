"use client";

import { Button, type ButtonProps } from "@ui/components/button";
import { cn } from "@ui/lib";
import { forwardRef } from "react";

interface CleanButtonProps extends ButtonProps {
	// Remove magical props, keep it simple
}

export const MagicalButton = forwardRef<HTMLButtonElement, CleanButtonProps>(
	({ className, children, ...props }, ref) => {
		return (
			<Button
				ref={ref}
				className={cn(
					"transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]",
					className,
				)}
				{...props}
			>
				{children}
			</Button>
		);
	},
);

MagicalButton.displayName = "MagicalButton";
