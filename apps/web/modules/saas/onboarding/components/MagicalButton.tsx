"use client";

import { cn } from "@ui/lib";
import { Button, type ButtonProps } from "@ui/components/button";
import { SparklesIcon } from "lucide-react";
import { forwardRef, useState } from "react";

interface MagicalButtonProps extends ButtonProps {
	magical?: boolean;
	sparkles?: boolean;
	gradient?: boolean;
}

export const MagicalButton = forwardRef<HTMLButtonElement, MagicalButtonProps>(
	({ className, magical = true, sparkles = false, gradient = false, children, ...props }, ref) => {
		const [isHovered, setIsHovered] = useState(false);

		return (
			<div className="relative group">
				{/* Magical glow effect */}
				{magical && isHovered && (
					<div className="absolute -inset-1 bg-gradient-to-r from-primary via-accent to-highlight rounded-lg blur-sm opacity-75 animate-pulse" />
				)}
				
				<Button
					ref={ref}
					className={cn(
						"relative transition-all duration-300",
						gradient && "bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90",
						magical && "hover:scale-105 hover:shadow-lg hover:shadow-primary/25",
						className
					)}
					onMouseEnter={() => setIsHovered(true)}
					onMouseLeave={() => setIsHovered(false)}
					{...props}
				>
					{/* Sparkles effect */}
					{sparkles && (
						<SparklesIcon className="w-4 h-4 mr-2 animate-pulse" />
					)}
					
					{children}

					{/* Floating sparkles on hover */}
					{magical && isHovered && (
						<div className="absolute inset-0 pointer-events-none">
							{[...Array(4)].map((_, i) => (
								<SparklesIcon
									key={i}
									className={cn(
										"absolute w-2 h-2 text-white/60 animate-ping",
										i === 0 && "top-1 right-1",
										i === 1 && "bottom-1 left-1 animation-delay-300",
										i === 2 && "top-1 left-1 animation-delay-600",
										i === 3 && "bottom-1 right-1 animation-delay-900"
									)}
									style={{
										animationDuration: "1.5s",
										animationDelay: `${i * 0.3}s`
									}}
								/>
							))}
						</div>
					)}
				</Button>
			</div>
		);
	}
);

MagicalButton.displayName = "MagicalButton";
