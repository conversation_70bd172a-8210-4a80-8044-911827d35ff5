"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { generateOrganizationSlug } from "@saas/organizations/lib/api";
import { WebsiteService } from "@saas/websites/lib/website-service";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { ArrowRightIcon, MapPinIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
	type BusinessInfo,
	extractPlacesIdFromUrl,
	isGoogleMapsUrl,
} from "../utils/google-maps";
import { AILoadingAnimation } from "./AILoadingAnimation";
import { ExtendedBusinessPreview } from "./BusinessPreview";
import { MagicalBackground } from "./MagicalBackground";
import { MagicalButton } from "./MagicalButton";
import { MagicalInput } from "./MagicalInput";
import { ManualBusinessForm } from "./ManualBusinessForm";

const formSchema = z.object({
	googleMapsUrl: z
		.string()
		.min(1, "Google Maps business link is required")
		.url("Please enter a valid URL")
		.refine((url) => isGoogleMapsUrl(url), {
			message: "Please enter a valid Google Maps business link",
		})
		.refine(
			(url) => {
				// Check if we can extract a Places ID
				return extractPlacesIdFromUrl(url) !== null;
			},
			{
				message:
					"Unable to extract business information from this link. Please ensure it's a direct link to your business on Google Maps.",
			},
		),
});

type FormValues = z.infer<typeof formSchema>;

export function OnboardingStep1({
	onCompleted,
}: { onCompleted: (organizationSlug?: string) => void }) {
	const t = useTranslations();
	const [isExtracting, setIsExtracting] = useState(false);
	const [businessInfo, setBusinessInfo] = useState<BusinessInfo | null>(null);
	const [showPreview, setShowPreview] = useState(false);
	const [isCreatingOrganization, setIsCreatingOrganization] = useState(false);
	const [showManualForm, setShowManualForm] = useState(false);
	const [inputValue, setInputValue] = useState("");
	const [isValidUrl, setIsValidUrl] = useState(false);

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			googleMapsUrl: "",
		},
	});

	const demoUrls = [
		{
			url: "https://maps.google.com/maps?cid=1234567890123456789",
			name: "☕ Artisan Coffee Shop",
			description: "Cozy neighborhood cafe",
		},
		{
			url: "https://www.google.com/maps/place/Restaurant/@40.7128,-74.0060,15z/data=!3m1!4b1!4m6!3m5!1s0x89c25a316e5b7c5d:0x9876543210fedcba",
			name: "🍽️ Fine Dining Restaurant",
			description: "Elegant dining experience",
		},
		{
			url: "https://maps.google.com/maps?cid=5555666677778888",
			name: "💻 Tech Repair Service",
			description: "Professional tech solutions",
		},
		{
			url: "https://maps.google.com/maps?cid=7777888899990000",
			name: "🌸 Boutique Florist",
			description: "Beautiful floral arrangements",
		},
		{
			url: "https://www.google.com/maps/place/Fitness/@41.8781,-87.6298,15z/data=!3m1!4b1!4m6!3m5!1s0x880e2ca55810a493:0x1111222233334444",
			name: "💪 Elite Fitness Center",
			description: "Premium fitness facility",
		},
	];

	const tryDemo = () => {
		const randomDemo =
			demoUrls[Math.floor(Math.random() * demoUrls.length)];
		form.setValue("googleMapsUrl", randomDemo.url);
		setInputValue(randomDemo.url);
		setIsValidUrl(true);
	};

	// URL validation effect
	useEffect(() => {
		const isValid = Boolean(
			inputValue &&
				isGoogleMapsUrl(inputValue) &&
				extractPlacesIdFromUrl(inputValue) !== null,
		);
		setIsValidUrl(isValid);
	}, [inputValue]);

	const handleConfirmBusiness = async () => {
		if (!businessInfo) {
			return;
		}

		setIsCreatingOrganization(true);

		try {
			// Create organization with the business name and information
			const organizationName =
				businessInfo.name || `Business (${businessInfo.placesId})`;

			const { error: orgError, data: newOrganization } =
				await authClient.organization.create({
					name: organizationName,
					slug: await generateOrganizationSlug(organizationName),
					metadata: {
						businessInfo,
						source: "google_maps",
						createdAt: new Date().toISOString(),
					},
				});

			if (orgError) {
				throw new Error(
					orgError.message || "Failed to create organization",
				);
			}

			// Set the new organization as active
			if (newOrganization) {
				await authClient.organization.setActive({
					organizationId: newOrganization.id,
				});

				// Generate website from business information
				try {
					await WebsiteService.generateWebsite(
						newOrganization.id,
						businessInfo,
					);
					console.log(
						"Website generation started for organization:",
						newOrganization.id,
					);
				} catch (websiteError) {
					console.error(
						"Failed to start website generation:",
						websiteError,
					);
					// Don't fail the onboarding if website generation fails
				}
			}

			// Complete onboarding - keep loading state until page transitions
			onCompleted(newOrganization?.slug);
		} catch (e) {
			form.setError("root", {
				type: "server",
				message: t("onboarding.notifications.accountSetupFailed"),
			});
			setIsCreatingOrganization(false);
		}
		// Note: Don't set setIsCreatingOrganization(false) on success
		// Keep the loading state until the page transitions
	};

	const handleManualBusinessSubmit = async (
		manualBusinessInfo: BusinessInfo,
	) => {
		setBusinessInfo(manualBusinessInfo);
		setShowPreview(true);
		setShowManualForm(false);
	};

	const handleBackToUrlEntry = () => {
		setShowManualForm(false);
		setShowPreview(false);
		setBusinessInfo(null);
		form.reset();
	};

	const onSubmit: SubmitHandler<FormValues> = async ({ googleMapsUrl }) => {
		form.clearErrors("root");
		setIsExtracting(true);

		try {
			// Use API route to extract business information with demo data
			const response = await fetch("/api/business/extract-from-maps", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ googleMapsUrl }),
			});

			if (!response.ok) {
				const errorData = await response.json();

				// Use improved error messages with suggestions
				const errorMessage = errorData.suggestion
					? `${errorData.error}. ${errorData.suggestion}`
					: errorData.error ||
						"Failed to extract business information";

				form.setError("googleMapsUrl", {
					type: "manual",
					message: errorMessage,
				});

				// For certain error types, we can suggest manual entry
				const canTryManualEntry = [
					"PLACES_ID_NOT_FOUND",
					"BUSINESS_INFO_EXTRACTION_FAILED",
					"UNSUPPORTED_URL_FORMAT",
				].includes(errorData.errorType);

				if (canTryManualEntry) {
					form.setError("root", {
						type: "manual",
						message: "manual_entry_available",
					});
				}

				return;
			}

			const { businessInfo: extractedBusinessInfo } =
				await response.json();

			// Store the business information and show preview
			setBusinessInfo(extractedBusinessInfo);
			setShowPreview(true);
		} catch (e) {
			form.setError("root", {
				type: "server",
				message: t("onboarding.notifications.accountSetupFailed"),
			});
		} finally {
			setIsExtracting(false);
		}
	};

	return (
		<MagicalBackground className="min-h-[500px] p-8">
			<div className="max-w-2xl mx-auto space-y-8">
				{showManualForm ? (
					<ManualBusinessForm
						onSubmit={handleManualBusinessSubmit}
						onBack={handleBackToUrlEntry}
						isSubmitting={isCreatingOrganization}
					/>
				) : !showPreview ? (
					<div className="space-y-8">
						{/* Clean Header */}
						<div className="text-center space-y-4">
							<h2 className="text-3xl font-bold text-foreground">
								Create your website
							</h2>
							<p className="text-lg text-muted-foreground max-w-lg mx-auto">
								Share your Google Maps business link and we'll
								create a beautiful website for you
							</p>
						</div>

						<Form {...form}>
							<form
								className="flex flex-col items-stretch gap-8 max-w-lg mx-auto animate-in fade-in duration-500"
								onSubmit={form.handleSubmit(onSubmit)}
							>
								<FormField
									control={form.control}
									name="googleMapsUrl"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="flex items-center gap-3 text-base font-medium group cursor-pointer">
												<div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/15 transition-colors duration-200">
													<MapPinIcon className="size-4 text-primary" />
												</div>
												<span className="group-hover:text-primary transition-colors duration-200">
													{t(
														"onboarding.business.googleMapsLink",
													)}
												</span>
											</FormLabel>
											<FormControl>
												<div className="space-y-4">
													<MagicalInput
														{...field}
														placeholder={t(
															"onboarding.business.googleMapsLinkPlaceholder",
														)}
														type="url"
														disabled={isExtracting}
														isValid={
															isValidUrl &&
															!isExtracting
														}
														isLoading={isExtracting}
														onChange={(
															e: React.ChangeEvent<HTMLInputElement>,
														) => {
															field.onChange(e);
															setInputValue(
																e.target.value,
															);
														}}
														className="text-lg py-3"
													/>

													{/* Demo suggestions */}
													<div className="text-center">
														<MagicalButton
															type="button"
															variant="ghost"
															size="sm"
															onClick={tryDemo}
															disabled={
																isExtracting
															}
															className="text-sm text-muted-foreground"
														>
															Try with demo
															business
														</MagicalButton>
													</div>
												</div>
											</FormControl>
											<FormDescription className="text-center">
												{t(
													"onboarding.business.googleMapsLinkDescription",
												)}
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								{form.formState.errors.root &&
									form.formState.errors.root.message ===
										"manual_entry_available" && (
										<div className="space-y-3 p-4 bg-gradient-to-r from-muted/50 to-accent/10 rounded-lg border border-accent/20">
											<p className="text-sm text-muted-foreground text-center">
												Having trouble with the URL? You
												can add your business
												information manually instead.
											</p>
											<MagicalButton
												type="button"
												variant="outline"
												size="sm"
												onClick={() =>
													setShowManualForm(true)
												}
												disabled={isExtracting}
												className="w-full"
											>
												Add Business Manually
											</MagicalButton>
										</div>
									)}

								<MagicalButton
									type="submit"
									loading={
										form.formState.isSubmitting ||
										isExtracting
									}
									disabled={
										form.formState.isSubmitting ||
										isExtracting
									}
									className="w-full py-3"
									variant="primary"
								>
									{isExtracting ? (
										<>
											{t(
												"onboarding.business.extracting",
											)}
										</>
									) : (
										<>
											{t("onboarding.continue")}
											<ArrowRightIcon className="ml-2 size-4" />
										</>
									)}
								</MagicalButton>
							</form>
						</Form>
					</div>
				) : (
					<div className="space-y-8 animate-in fade-in duration-700">
						{/* Business Preview */}
						{businessInfo && (
							<div className="text-center space-y-6">
								<h3 className="text-xl font-semibold text-foreground">
									Business Information
								</h3>
								<ExtendedBusinessPreview
									businessInfo={businessInfo}
									className="mx-auto max-w-md"
								/>
							</div>
						)}

						{!isCreatingOrganization ? (
							<div className="space-y-6 max-w-md mx-auto">
								<div className="text-center">
									<p className="text-muted-foreground">
										Please review your business information
										and confirm to create your website.
									</p>
								</div>

								<div className="flex gap-4">
									<MagicalButton
										variant="outline"
										onClick={handleBackToUrlEntry}
										className="flex-1"
										disabled={isCreatingOrganization}
									>
										Go Back
									</MagicalButton>
									<MagicalButton
										onClick={handleConfirmBusiness}
										className="flex-1"
										disabled={isCreatingOrganization}
										variant="primary"
									>
										Create Website
										<ArrowRightIcon className="ml-2 size-4" />
									</MagicalButton>
								</div>
							</div>
						) : (
							<AILoadingAnimation
								message="Creating your website"
								className="py-12"
							/>
						)}
					</div>
				)}
			</div>
		</MagicalBackground>
	);
}
