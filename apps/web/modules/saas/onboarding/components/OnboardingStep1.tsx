"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { generateOrganizationSlug } from "@saas/organizations/lib/api";
import { WebsiteService } from "@saas/websites/lib/website-service";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import {
	ArrowRightIcon,
	BrainIcon,
	MapPinIcon,
	SparklesIcon,
	WandIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
	type BusinessInfo,
	extractPlacesIdFromUrl,
	isGoogleMapsUrl,
} from "../utils/google-maps";
import { ExtendedBusinessPreview } from "./BusinessPreview";
import { ManualBusinessForm } from "./ManualBusinessForm";

const formSchema = z.object({
	googleMapsUrl: z
		.string()
		.min(1, "Google Maps business link is required")
		.url("Please enter a valid URL")
		.refine((url) => isGoogleMapsUrl(url), {
			message: "Please enter a valid Google Maps business link",
		})
		.refine(
			(url) => {
				// Check if we can extract a Places ID
				return extractPlacesIdFromUrl(url) !== null;
			},
			{
				message:
					"Unable to extract business information from this link. Please ensure it's a direct link to your business on Google Maps.",
			},
		),
});

type FormValues = z.infer<typeof formSchema>;

export function OnboardingStep1({
	onCompleted,
}: { onCompleted: (organizationSlug?: string) => void }) {
	const t = useTranslations();
	const [isExtracting, setIsExtracting] = useState(false);
	const [businessInfo, setBusinessInfo] = useState<BusinessInfo | null>(null);
	const [showPreview, setShowPreview] = useState(false);
	const [isCreatingOrganization, setIsCreatingOrganization] = useState(false);
	const [showManualForm, setShowManualForm] = useState(false);
	const [inputValue, setInputValue] = useState("");
	const [isValidUrl, setIsValidUrl] = useState(false);
	const [showSuggestions, setShowSuggestions] = useState(false);

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			googleMapsUrl: "",
		},
	});

	const demoUrls = [
		{
			url: "https://maps.google.com/maps?cid=1234567890123456789",
			name: "☕ Artisan Coffee Shop",
			description: "Cozy neighborhood cafe",
		},
		{
			url: "https://www.google.com/maps/place/Restaurant/@40.7128,-74.0060,15z/data=!3m1!4b1!4m6!3m5!1s0x89c25a316e5b7c5d:0x9876543210fedcba",
			name: "🍽️ Fine Dining Restaurant",
			description: "Elegant dining experience",
		},
		{
			url: "https://maps.google.com/maps?cid=5555666677778888",
			name: "💻 Tech Repair Service",
			description: "Professional tech solutions",
		},
		{
			url: "https://maps.google.com/maps?cid=7777888899990000",
			name: "🌸 Boutique Florist",
			description: "Beautiful floral arrangements",
		},
		{
			url: "https://www.google.com/maps/place/Fitness/@41.8781,-87.6298,15z/data=!3m1!4b1!4m6!3m5!1s0x880e2ca55810a493:0x1111222233334444",
			name: "💪 Elite Fitness Center",
			description: "Premium fitness facility",
		},
	];

	const tryDemo = () => {
		const randomDemo =
			demoUrls[Math.floor(Math.random() * demoUrls.length)];
		form.setValue("googleMapsUrl", randomDemo.url);
		setInputValue(randomDemo.url);
		setIsValidUrl(true);
	};

	// URL validation effect
	useEffect(() => {
		const isValid = Boolean(
			inputValue &&
				isGoogleMapsUrl(inputValue) &&
				extractPlacesIdFromUrl(inputValue) !== null,
		);
		setIsValidUrl(isValid);
	}, [inputValue]);

	const handleConfirmBusiness = async () => {
		if (!businessInfo) {
			return;
		}

		setIsCreatingOrganization(true);

		try {
			// Create organization with the business name and information
			const organizationName =
				businessInfo.name || `Business (${businessInfo.placesId})`;

			const { error: orgError, data: newOrganization } =
				await authClient.organization.create({
					name: organizationName,
					slug: await generateOrganizationSlug(organizationName),
					metadata: {
						businessInfo,
						source: "google_maps",
						createdAt: new Date().toISOString(),
					},
				});

			if (orgError) {
				throw new Error(
					orgError.message || "Failed to create organization",
				);
			}

			// Set the new organization as active
			if (newOrganization) {
				await authClient.organization.setActive({
					organizationId: newOrganization.id,
				});

				// Generate website from business information
				try {
					await WebsiteService.generateWebsite(
						newOrganization.id,
						businessInfo,
					);
					console.log(
						"Website generation started for organization:",
						newOrganization.id,
					);
				} catch (websiteError) {
					console.error(
						"Failed to start website generation:",
						websiteError,
					);
					// Don't fail the onboarding if website generation fails
				}
			}

			// Complete onboarding - keep loading state until page transitions
			onCompleted(newOrganization?.slug);
		} catch (e) {
			form.setError("root", {
				type: "server",
				message: t("onboarding.notifications.accountSetupFailed"),
			});
			setIsCreatingOrganization(false);
		}
		// Note: Don't set setIsCreatingOrganization(false) on success
		// Keep the loading state until the page transitions
	};

	const handleManualBusinessSubmit = async (
		manualBusinessInfo: BusinessInfo,
	) => {
		setBusinessInfo(manualBusinessInfo);
		setShowPreview(true);
		setShowManualForm(false);
	};

	const handleBackToUrlEntry = () => {
		setShowManualForm(false);
		setShowPreview(false);
		setBusinessInfo(null);
		form.reset();
	};

	const onSubmit: SubmitHandler<FormValues> = async ({ googleMapsUrl }) => {
		form.clearErrors("root");
		setIsExtracting(true);

		try {
			// Use API route to extract business information with demo data
			const response = await fetch("/api/business/extract-from-maps", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ googleMapsUrl }),
			});

			if (!response.ok) {
				const errorData = await response.json();

				// Use improved error messages with suggestions
				const errorMessage = errorData.suggestion
					? `${errorData.error}. ${errorData.suggestion}`
					: errorData.error ||
						"Failed to extract business information";

				form.setError("googleMapsUrl", {
					type: "manual",
					message: errorMessage,
				});

				// For certain error types, we can suggest manual entry
				const canTryManualEntry = [
					"PLACES_ID_NOT_FOUND",
					"BUSINESS_INFO_EXTRACTION_FAILED",
					"UNSUPPORTED_URL_FORMAT",
				].includes(errorData.errorType);

				if (canTryManualEntry) {
					form.setError("root", {
						type: "manual",
						message: "manual_entry_available",
					});
				}

				return;
			}

			const { businessInfo: extractedBusinessInfo } =
				await response.json();

			// Store the business information and show preview
			setBusinessInfo(extractedBusinessInfo);
			setShowPreview(true);
		} catch (e) {
			form.setError("root", {
				type: "server",
				message: t("onboarding.notifications.accountSetupFailed"),
			});
		} finally {
			setIsExtracting(false);
		}
	};

	return (
		<MagicalBackground className="min-h-[600px] p-6 rounded-2xl">
			<FloatingParticles count={15} />
			<div className="space-y-8 relative z-10">
				{showManualForm ? (
					<ManualBusinessForm
						onSubmit={handleManualBusinessSubmit}
						onBack={handleBackToUrlEntry}
						isSubmitting={isCreatingOrganization}
					/>
				) : !showPreview ? (
					<div className="space-y-8">
						{/* Magical Header */}
						<div className="text-center space-y-4">
							<div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full mb-4">
								<WandIcon className="w-8 h-8 text-white" />
							</div>
							<h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
								Let's Create Your Website Magic ✨
							</h2>
							<p className="text-muted-foreground max-w-md mx-auto">
								Share your Google Maps business link and watch
								AI transform it into a stunning website
							</p>
						</div>

						<Form {...form}>
							<form
								className="flex flex-col items-stretch gap-8 max-w-lg mx-auto"
								onSubmit={form.handleSubmit(onSubmit)}
							>
								<FormField
									control={form.control}
									name="googleMapsUrl"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="flex items-center gap-2 text-lg font-semibold">
												<div className="p-2 bg-primary/10 rounded-lg">
													<MapPinIcon className="size-5 text-primary" />
												</div>
												{t(
													"onboarding.business.googleMapsLink",
												)}
											</FormLabel>
											<FormControl>
												<div className="space-y-4">
													<MagicalInput
														{...field}
														placeholder={t(
															"onboarding.business.googleMapsLinkPlaceholder",
														)}
														type="url"
														disabled={isExtracting}
														isValid={
															isValidUrl &&
															!isExtracting
														}
														isLoading={isExtracting}
														onChange={(e) => {
															field.onChange(e);
															setInputValue(
																e.target.value,
															);
														}}
														className="text-lg py-3"
													/>

													{/* Demo suggestions */}
													<div className="space-y-3">
														<div className="flex items-center justify-between">
															<span className="text-sm text-muted-foreground">
																Try a demo
																business:
															</span>
															<MagicalButton
																type="button"
																variant="ghost"
																size="sm"
																onClick={
																	tryDemo
																}
																disabled={
																	isExtracting
																}
																sparkles
																className="text-xs"
															>
																<SparklesIcon className="w-3 h-3 mr-1" />
																Random Demo
															</MagicalButton>
														</div>

														{/* Demo URL grid */}
														<div className="grid grid-cols-1 gap-2">
															{demoUrls
																.slice(0, 3)
																.map(
																	(
																		demo,
																		index,
																	) => (
																		<button
																			key={
																				index
																			}
																			type="button"
																			onClick={() => {
																				form.setValue(
																					"googleMapsUrl",
																					demo.url,
																				);
																				setInputValue(
																					demo.url,
																				);
																				setIsValidUrl(
																					true,
																				);
																			}}
																			disabled={
																				isExtracting
																			}
																			className="text-left p-3 rounded-lg border border-border/50 hover:border-primary/50 hover:bg-primary/5 transition-all duration-200 group"
																		>
																			<div className="flex items-center gap-3">
																				<div className="text-lg">
																					{
																						demo.name.split(
																							" ",
																						)[0]
																					}
																				</div>
																				<div>
																					<div className="font-medium text-sm group-hover:text-primary transition-colors">
																						{demo.name.substring(
																							2,
																						)}
																					</div>
																					<div className="text-xs text-muted-foreground">
																						{
																							demo.description
																						}
																					</div>
																				</div>
																			</div>
																		</button>
																	),
																)}
														</div>
													</div>
												</div>
											</FormControl>
											<FormDescription className="text-center">
												{t(
													"onboarding.business.googleMapsLinkDescription",
												)}
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								{form.formState.errors.root &&
									form.formState.errors.root.message ===
										"manual_entry_available" && (
										<div className="space-y-3 p-4 bg-gradient-to-r from-muted/50 to-accent/10 rounded-lg border border-accent/20">
											<p className="text-sm text-muted-foreground text-center">
												Having trouble with the URL? You
												can add your business
												information manually instead.
											</p>
											<MagicalButton
												type="button"
												variant="outline"
												size="sm"
												onClick={() =>
													setShowManualForm(true)
												}
												disabled={isExtracting}
												className="w-full"
												magical
											>
												Add Business Manually
											</MagicalButton>
										</div>
									)}

								<MagicalButton
									type="submit"
									loading={
										form.formState.isSubmitting ||
										isExtracting
									}
									disabled={
										form.formState.isSubmitting ||
										isExtracting
									}
									className="w-full py-4 text-lg"
									gradient
									sparkles
								>
									{isExtracting ? (
										<>
											<BrainIcon className="w-5 h-5 mr-2 animate-pulse" />
											{t(
												"onboarding.business.extracting",
											)}
										</>
									) : (
										<>
											<SparklesIcon className="w-5 h-5 mr-2" />
											{t("onboarding.continue")}
											<ArrowRightIcon className="ml-2 size-5" />
										</>
									)}
								</MagicalButton>
							</form>
						</Form>
					</div>
				) : (
					<div className="space-y-8">
						{/* Business Preview with magical styling */}
						{businessInfo && (
							<div className="text-center space-y-6">
								<div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-success to-accent rounded-full mb-4">
									<SparklesIcon className="w-8 h-8 text-white animate-pulse" />
								</div>
								<h3 className="text-xl font-bold text-foreground">
									✨ Business Information Extracted!
								</h3>
								<ExtendedBusinessPreview
									businessInfo={businessInfo}
									className="mx-auto max-w-md transform hover:scale-105 transition-transform duration-300"
								/>
							</div>
						)}

						{!isCreatingOrganization ? (
							<div className="space-y-6 max-w-md mx-auto">
								<div className="text-center">
									<p className="text-muted-foreground">
										Please review your business information
										and confirm to create your magical
										website.
									</p>
								</div>

								<div className="flex gap-4">
									<MagicalButton
										variant="outline"
										onClick={handleBackToUrlEntry}
										className="flex-1"
										disabled={isCreatingOrganization}
									>
										Go Back
									</MagicalButton>
									<MagicalButton
										onClick={handleConfirmBusiness}
										className="flex-1"
										disabled={isCreatingOrganization}
										gradient
										sparkles
									>
										Create Website
										<WandIcon className="ml-2 size-4" />
									</MagicalButton>
								</div>
							</div>
						) : (
							<AILoadingAnimation
								message="Creating your magical website"
								variant="wand"
								className="py-12"
							/>
						)}
					</div>
				)}
			</div>
		</MagicalBackground>
	);
}
