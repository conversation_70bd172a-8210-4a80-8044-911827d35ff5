"use client";

import { cn } from "@ui/lib";
import { useEffect, useState } from "react";

interface Particle {
	id: number;
	x: number;
	y: number;
	size: number;
	duration: number;
	delay: number;
}

interface FloatingParticlesProps {
	count?: number;
	className?: string;
}

export function FloatingParticles({ count = 20, className }: FloatingParticlesProps) {
	const [particles, setParticles] = useState<Particle[]>([]);
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		const newParticles: Particle[] = [];
		for (let i = 0; i < count; i++) {
			newParticles.push({
				id: i,
				x: Math.random() * 100,
				y: Math.random() * 100,
				size: Math.random() * 4 + 1,
				duration: Math.random() * 10 + 10,
				delay: Math.random() * 5,
			});
		}
		setParticles(newParticles);
		setMounted(true);
	}, [count]);

	if (!mounted) return null;

	return (
		<div className={cn("absolute inset-0 overflow-hidden pointer-events-none", className)}>
			{particles.map((particle) => (
				<div
					key={particle.id}
					className="absolute w-1 h-1 bg-primary/20 rounded-full animate-pulse"
					style={{
						left: `${particle.x}%`,
						top: `${particle.y}%`,
						width: `${particle.size}px`,
						height: `${particle.size}px`,
						animationDuration: `${particle.duration}s`,
						animationDelay: `${particle.delay}s`,
						transform: `translateY(${Math.sin(particle.x) * 20}px)`,
					}}
				/>
			))}
		</div>
	);
}
