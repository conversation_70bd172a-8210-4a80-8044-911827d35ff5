"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Textarea } from "@ui/components/textarea";
import {
	ExternalLinkIcon,
	EyeIcon,
	GlobeIcon,
	PaletteIcon,
	SaveIcon,
	SettingsIcon,
} from "lucide-react";
import { useState } from "react";
import { WebsiteService } from "../lib/website-service";
import {
	type GeneratedWebsite,
	WebsiteFeature,
} from "../utils/website-generator";

interface WebsiteSettingsModalProps {
	website: GeneratedWebsite;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onWebsiteUpdate: (website: GeneratedWebsite) => void;
}

export function WebsiteSettingsModal({
	website,
	open,
	onOpenChange,
	onWebsiteUpdate,
}: WebsiteSettingsModalProps) {
	const [saving, setSaving] = useState(false);
	const [config, setConfig] = useState(website.config);
	const [businessInfo, setBusinessInfo] = useState(website.businessInfo);

	const handleSave = async () => {
		setSaving(true);
		try {
			const updatedWebsite = await WebsiteService.updateWebsiteConfig(
				website.id,
				config,
			);
			if (updatedWebsite) {
				onWebsiteUpdate(updatedWebsite);
				onOpenChange(false);
			}
		} catch (error) {
			console.error("Failed to update website:", error);
		} finally {
			setSaving(false);
		}
	};

	const toggleFeature = (feature: WebsiteFeature) => {
		setConfig((prev) => ({
			...prev,
			features: prev.features.includes(feature)
				? prev.features.filter((f) => f !== feature)
				: [...prev.features, feature],
		}));
	};

	const templateInfo = WebsiteService.getTemplateInfo(config.template);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<SettingsIcon className="size-5" />
						Website Settings
					</DialogTitle>
					<DialogDescription>
						Customize your website appearance and features
					</DialogDescription>
				</DialogHeader>

				<Tabs defaultValue="design" className="w-full">
					<TabsList className="grid w-full grid-cols-4">
						<TabsTrigger
							value="design"
							className="flex items-center gap-2"
						>
							<PaletteIcon className="size-4" />
							Design
						</TabsTrigger>
						<TabsTrigger
							value="content"
							className="flex items-center gap-2"
						>
							<GlobeIcon className="size-4" />
							Content
						</TabsTrigger>
						<TabsTrigger
							value="features"
							className="flex items-center gap-2"
						>
							<SettingsIcon className="size-4" />
							Features
						</TabsTrigger>
						<TabsTrigger
							value="preview"
							className="flex items-center gap-2"
						>
							<EyeIcon className="size-4" />
							Preview
						</TabsTrigger>
					</TabsList>

					<TabsContent value="design" className="space-y-6">
						<Card>
							<CardHeader>
								<CardTitle>Template</CardTitle>
								<CardDescription>
									Your website is using the{" "}
									{templateInfo.name} template
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="flex items-center gap-4 p-4 border rounded-lg">
									<div
										className="w-12 h-12 rounded-lg"
										style={{
											backgroundColor: templateInfo.color,
										}}
									/>
									<div className="flex-1">
										<h4 className="font-medium">
											{templateInfo.name}
										</h4>
										<p className="text-sm text-muted-foreground">
											{templateInfo.description}
										</p>
									</div>
									<Badge status="success">Active</Badge>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Colors</CardTitle>
								<CardDescription>
									Customize your website's color scheme
								</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="grid grid-cols-2 gap-4">
									<div className="space-y-2">
										<Label htmlFor="primaryColor">
											Primary Color
										</Label>
										<div className="flex items-center gap-2">
											<Input
												id="primaryColor"
												type="color"
												value={config.primaryColor}
												onChange={(e) =>
													setConfig((prev) => ({
														...prev,
														primaryColor:
															e.target.value,
													}))
												}
												className="w-16 h-10 p-1"
											/>
											<Input
												value={config.primaryColor}
												onChange={(e) =>
													setConfig((prev) => ({
														...prev,
														primaryColor:
															e.target.value,
													}))
												}
												placeholder="#2563eb"
											/>
										</div>
									</div>
									<div className="space-y-2">
										<Label htmlFor="secondaryColor">
											Secondary Color
										</Label>
										<div className="flex items-center gap-2">
											<Input
												id="secondaryColor"
												type="color"
												value={config.secondaryColor}
												onChange={(e) =>
													setConfig((prev) => ({
														...prev,
														secondaryColor:
															e.target.value,
													}))
												}
												className="w-16 h-10 p-1"
											/>
											<Input
												value={config.secondaryColor}
												onChange={(e) =>
													setConfig((prev) => ({
														...prev,
														secondaryColor:
															e.target.value,
													}))
												}
												placeholder="#64748b"
											/>
										</div>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Layout</CardTitle>
								<CardDescription>
									Choose your website layout style
								</CardDescription>
							</CardHeader>
							<CardContent>
								<Select
									value={config.layout}
									onValueChange={(
										value: "modern" | "classic" | "minimal",
									) =>
										setConfig((prev) => ({
											...prev,
											layout: value,
										}))
									}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="modern">
											Modern
										</SelectItem>
										<SelectItem value="classic">
											Classic
										</SelectItem>
										<SelectItem value="minimal">
											Minimal
										</SelectItem>
									</SelectContent>
								</Select>
							</CardContent>
						</Card>
					</TabsContent>

					<TabsContent value="content" className="space-y-6">
						<Card>
							<CardHeader>
								<CardTitle>Business Information</CardTitle>
								<CardDescription>
									Update your business details displayed on
									the website
								</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="space-y-2">
									<Label htmlFor="businessName">
										Business Name
									</Label>
									<Input
										id="businessName"
										value={businessInfo.name || ""}
										onChange={(e) =>
											setBusinessInfo((prev) => ({
												...prev,
												name: e.target.value,
											}))
										}
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="businessAddress">
										Address
									</Label>
									<Textarea
										id="businessAddress"
										value={businessInfo.address || ""}
										onChange={(e) =>
											setBusinessInfo((prev) => ({
												...prev,
												address: e.target.value,
											}))
										}
										rows={2}
									/>
								</div>
								<div className="grid grid-cols-2 gap-4">
									<div className="space-y-2">
										<Label htmlFor="businessPhone">
											Phone
										</Label>
										<Input
											id="businessPhone"
											value={businessInfo.phone || ""}
											onChange={(e) =>
												setBusinessInfo((prev) => ({
													...prev,
													phone: e.target.value,
												}))
											}
										/>
									</div>
									<div className="space-y-2">
										<Label htmlFor="businessWebsite">
											Website
										</Label>
										<Input
											id="businessWebsite"
											value={businessInfo.website || ""}
											onChange={(e) =>
												setBusinessInfo((prev) => ({
													...prev,
													website: e.target.value,
												}))
											}
										/>
									</div>
								</div>
							</CardContent>
						</Card>
					</TabsContent>

					<TabsContent value="features" className="space-y-6">
						<Card>
							<CardHeader>
								<CardTitle>Website Features</CardTitle>
								<CardDescription>
									Enable or disable features for your website
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="space-y-4">
									{Object.values(WebsiteFeature).map(
										(feature) => {
											const isEnabled =
												config.features.includes(
													feature,
												);
											const featureLabels = {
												[WebsiteFeature.CONTACT_FORM]:
													"Contact Form",
												[WebsiteFeature.ONLINE_BOOKING]:
													"Online Booking",
												[WebsiteFeature.GALLERY]:
													"Photo Gallery",
												[WebsiteFeature.MENU]:
													"Menu/Services List",
												[WebsiteFeature.TESTIMONIALS]:
													"Customer Testimonials",
												[WebsiteFeature.LOCATION_MAP]:
													"Location Map",
												[WebsiteFeature.SOCIAL_LINKS]:
													"Social Media Links",
												[WebsiteFeature.BUSINESS_HOURS]:
													"Business Hours",
												[WebsiteFeature.SERVICES_LIST]:
													"Services List",
												[WebsiteFeature.TEAM_SECTION]:
													"Team Section",
											};

											return (
												<div
													key={feature}
													className="flex items-center justify-between"
												>
													<div>
														<Label
															htmlFor={feature}
															className="font-medium"
														>
															{
																featureLabels[
																	feature
																]
															}
														</Label>
														<p className="text-sm text-muted-foreground">
															{feature
																.replace(
																	/_/g,
																	" ",
																)
																.toLowerCase()}
														</p>
													</div>
													<Switch
														id={feature}
														checked={isEnabled}
														onCheckedChange={() =>
															toggleFeature(
																feature,
															)
														}
													/>
												</div>
											);
										},
									)}
								</div>
							</CardContent>
						</Card>
					</TabsContent>

					<TabsContent value="preview" className="space-y-6">
						<Card>
							<CardHeader>
								<CardTitle>Website Preview</CardTitle>
								<CardDescription>
									See how your website will look with current
									settings
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
									<div className="text-center">
										<GlobeIcon className="size-12 mx-auto text-muted-foreground mb-4" />
										<p className="text-muted-foreground">
											Website preview will be available
											soon
										</p>
										<Button
											variant="outline"
											className="mt-4"
											asChild
										>
											<a
												href={website.url}
												target="_blank"
												rel="noopener noreferrer"
											>
												<ExternalLinkIcon className="size-4 mr-2" />
												View Live Site
											</a>
										</Button>
									</div>
								</div>
							</CardContent>
						</Card>
					</TabsContent>
				</Tabs>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
					>
						Cancel
					</Button>
					<Button onClick={handleSave} loading={saving}>
						<SaveIcon className="size-4 mr-2" />
						Save Changes
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
